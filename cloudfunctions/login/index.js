// 云函数入口文件
const cloud = require('wx-server-sdk')
const fs = require('fs');
const path = require('path');

cloud.init({
  env: 'cloud1-5g83xj46e99cc3de'
})

const db = cloud.database()

// 随机生成搞怪有创意的用户名
function generateFunnyUsername() {
  const adjectives = ['暴走', '呆萌', '狂野', '神秘', '魔性', '闪电', '咸鱼', '无敌', '迷糊', '机智', '奶凶', '暴躁', '佛系', '超能', '暗黑', '小小', '大大', '快乐', '忧伤', '高冷', '热情', '二次元', '沙雕', '憨憨', '灵魂', '爆炸', '奇葩', '骚气', '无聊', '带感', '超萌'];
  const animals = ['猫', '狗', '企鹅', '熊猫', '仓鼠', '柯基', '龙猫', '刺猬', '章鱼', '小鸡', '恐龙', '松鼠', '鹦鹉', '兔子', '河马', '考拉', '狐狸', '鸭子', '大鹅', '鲨鱼', '小猪', '老虎', '狮子', '豹子', '乌龟', '蛇', '青蛙', '蝙蝠', '蜜蜂', '蜗牛'];
  const suffix = Math.floor(Math.random() * 10000);
  const adj = adjectives[Math.floor(Math.random() * adjectives.length)];
  const animal = animals[Math.floor(Math.random() * animals.length)];
  return `${adj}${animal}${suffix}`;
}

// 云函数入口函数
exports.main = async (event, context) => {
  const wxContext = cloud.getWXContext()
  const { code } = event

  try {
    // 获取用户openid
    const openid = wxContext.OPENID
    const unionid = wxContext.UNIONID

    console.log('用户登录:', { openid, unionid })

    // 检查用户是否已存在
    const userResult = await db.collection('users').where({
      openid: openid
    }).get()

    if (userResult.data.length > 0) {
      // 用户已存在，更新登录时间
      await db.collection('users').where({
        openid: openid
      }).update({
        data: {
          updateTime: new Date()
        }
      });

      // 重新查一次，返回最新 userInfo
      const latestUser = await db.collection('users').where({ openid: openid }).get();
      return {
        success: true,
        userInfo: latestUser.data[0],
        message: '登录成功'
      }
    } else {
      // 新用户，创建用户记录
      // 生成搞怪有创意的用户名
      const funnyName = generateFunnyUsername();
      // 使用本地 images/default.jpg 作为默认头像
      // 这里只能存一个标记，前端渲染时用本地图片
      const newUser = {
        openid: openid,
        unionid: unionid,
        createTime: new Date(),
        updateTime: new Date(),
        avatarUrl: 'local_default', // 标记为本地默认头像，前端识别
        nickName: funnyName
      }

      const addResult = await db.collection('users').add({
        data: newUser
      })

      console.log('新用户注册成功:', openid, funnyName)
      return {
        success: true,
        userInfo: {
          ...newUser,
          _id: addResult._id
        },
        message: '注册成功'
      }
    }
  } catch (error) {
    console.error('登录失败:', error)
    return {
      success: false,
      message: '登录失败，请重试'
    }
  }
} 