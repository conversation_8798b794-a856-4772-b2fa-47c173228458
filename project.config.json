{"description": "项目配置文件", "miniprogramRoot": "miniprogram/", "cloudfunctionRoot": "cloudfunctions/", "compileType": "miniprogram", "setting": {"useCompilerPlugins": ["typescript", "less"], "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "coverView": false, "postcss": false, "minified": false, "enhance": true, "showShadowRootInWxmlPanel": false, "packNpmRelationList": [{"packageJsonPath": "miniprogram/package.json", "miniprogramNpmDistDir": "miniprogram/miniprogram_npm"}], "ignoreUploadUnusedFiles": true, "compileHotReLoad": false, "skylineRenderEnable": true, "es6": true, "compileWorklet": false, "uglifyFileName": false, "uploadWithSourceMap": true, "packNpmManually": true, "minifyWXSS": true, "minifyWXML": true, "localPlugins": false, "condition": false, "swc": false, "disableSWC": true, "disableUseStrict": false}, "simulatorType": "wechat", "simulatorPluginLibVersion": {}, "condition": {}, "srcMiniprogramRoot": "miniprogram/", "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}, "libVersion": "2.32.3", "packOptions": {"ignore": [], "include": []}, "compilerOptions": {"baseUrl": ".", "types": ["miniprogram-api-typings"], "paths": {"@vant/weapp/*": ["path/to/node_modules/@vant/weapp/dist/*"]}, "lib": ["ES6"]}, "appid": "wxa060e4152d5afb27", "cloudfunctionTemplateRoot": "cloudfunctionTemplate/"}