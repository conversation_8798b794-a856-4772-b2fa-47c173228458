// 全局变量
@primary-color: #1890ff;
@background-color: #f8f9fa;
@text-color: #333;
@text-secondary: #666;
@border-color: #e8e8e8;
@success-color: #52c41a;
@warning-color: #faad14;
@error-color: #f5222d;

// 重置样式
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background-color: @background-color;
    color: @text-color;
    line-height: 1.5;
}

// 通用样式
.container {
    max-width: 375px;
    margin: 0 auto;
    background: #fff;
    min-height: 100vh;
    position: relative;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    
    &.btn-primary {
        background: @primary-color;
        color: white;
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
        
        &:hover {
            background: darken(@primary-color, 10%);
            transform: translateY(-1px);
        }
    }
    
    &.btn-large {
        padding: 16px 32px;
        font-size: 18px;
    }
}

.card {
    background: #fff;
    border-radius: 12px;
    padding: 16px;
    margin-bottom: 16px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.flex {
    display: flex;
    
    &.flex-center {
        align-items: center;
        justify-content: center;
    }
    
    &.flex-between {
        justify-content: space-between;
    }
    
    &.flex-column {
        flex-direction: column;
    }
}

.text-center {
    text-align: center;
}

.mb-16 {
    margin-bottom: 16px;
}

.mt-16 {
    margin-top: 16px;
}

.p-16 {
    padding: 16px;
}

// 底部导航栏
.tab-bar {
    position: fixed;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 375px;
    height: 60px;
    background: #fff;
    border-top: 1px solid @border-color;
    display: flex;
    align-items: center;
    justify-content: space-around;
    z-index: 1000;
    
    .tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        
        .icon {
            width: 24px;
            height: 24px;
            margin-bottom: 4px;
            background: @text-secondary;
            border-radius: 50%;
        }
        
        .text {
            font-size: 12px;
            color: @text-secondary;
        }
        
        &.active {
            .icon {
                background: @primary-color;
            }
            
            .text {
                color: @primary-color;
            }
        }
    }
}

// 内容区域
.content {
    padding-bottom: 80px;
} 