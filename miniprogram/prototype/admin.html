<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>后台管理 - 临校速通</title>
  <link rel="stylesheet/less" type="text/css" href="styles.less" />
  <script src="https://cdn.jsdelivr.net/npm/less@4.1.1/dist/less.min.js"></script>
  <style>
    body {
      margin: 0;
      background: #f5faff;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    .admin-header {
      background: #1989fa;
      color: #fff;
      padding: 24px 0 16px 0;
      text-align: center;
      font-size: 22px;
      font-weight: bold;
      letter-spacing: 2px;
      border-radius: 0 0 24px 24px;
      box-shadow: 0 2px 8px rgba(25,137,250,0.08);
    }
    .admin-section {
      margin: 18px 16px;
      background: #fff;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(25,137,250,0.06);
      padding: 18px 16px 10px 16px;
    }
    .section-title {
      font-size: 16px;
      color: #1989fa;
      font-weight: bold;
      margin-bottom: 12px;
      display: flex;
      align-items: center;
      gap: 8px;
    }
    .section-title .iconfont {
      font-size: 18px;
    }
    .admin-form-row {
      display: flex;
      align-items: center;
      margin-bottom: 12px;
      gap: 10px;
    }
    .admin-form-label {
      min-width: 72px;
      color: #333;
      font-size: 15px;
    }
    .admin-form-input, .admin-form-select {
      flex: 1;
      padding: 8px 10px;
      border: 1px solid #e5eaf3;
      border-radius: 6px;
      font-size: 15px;
      background: #f7fafd;
      outline: none;
    }
    .admin-btn {
      background: #1989fa;
      color: #fff;
      border: none;
      border-radius: 6px;
      padding: 8px 18px;
      font-size: 15px;
      font-weight: bold;
      cursor: pointer;
      margin-left: 8px;
      transition: background 0.2s;
    }
    .admin-btn:active {
      background: #176fd6;
    }
    .admin-table {
      width: 100%;
      border-collapse: collapse;
      margin-top: 10px;
      font-size: 14px;
      background: #fafdff;
    }
    .admin-table th, .admin-table td {
      border: 1px solid #e5eaf3;
      padding: 8px 6px;
      text-align: center;
    }
    .admin-table th {
      background: #eaf6ff;
      color: #1989fa;
      font-weight: bold;
    }
    .admin-table tr:nth-child(even) {
      background: #f5faff;
    }
    .admin-export-btn {
      background: #fff;
      color: #1989fa;
      border: 1px solid #1989fa;
      border-radius: 6px;
      padding: 6px 14px;
      font-size: 14px;
      margin-bottom: 8px;
      cursor: pointer;
      float: right;
      margin-top: -6px;
    }
    .admin-export-btn:active {
      background: #eaf6ff;
    }
    .admin-tip {
      color: #999;
      font-size: 13px;
      margin-bottom: 8px;
    }
    @media (max-width: 420px) {
      .admin-section { margin: 8px 2px; padding: 10px 4px; }
    }
  </style>
</head>
<body>
  <div class="admin-header">后台管理</div>

  <!-- 1. 默认出发地/目的地配置 -->
  <div class="admin-section">
    <div class="section-title">🚩 默认出发地/目的地配置</div>
    <div class="admin-form-row">
      <div class="admin-form-label">默认出发地</div>
      <input class="admin-form-input" placeholder="如：济南" />
    </div>
    <div class="admin-form-row">
      <div class="admin-form-label">默认目的地</div>
      <input class="admin-form-input" placeholder="如：临沂" />
    </div>
    <button class="admin-btn">保存配置</button>
  </div>

  <!-- 2. 常用线路配置 -->
  <div class="admin-section">
    <div class="section-title">🛣️ 常用线路配置</div>
    <div class="admin-form-row">
      <div class="admin-form-label">出发地</div>
      <input class="admin-form-input" placeholder="如：济南" />
      <div class="admin-form-label">目的地</div>
      <input class="admin-form-input" placeholder="如：临沂" />
      <div class="admin-form-label">价格</div>
      <input class="admin-form-input" placeholder="如：45" type="number" style="width:60px;" />
      <button class="admin-btn">添加</button>
    </div>
    <div class="admin-tip">已配置线路：</div>
    <table class="admin-table">
      <thead>
        <tr><th>出发地</th><th>目的地</th><th>价格</th><th>操作</th></tr>
      </thead>
      <tbody>
        <tr><td>济南</td><td>临沂</td><td>45</td><td><button class="admin-btn" style="background:#f56c6c;">删除</button></td></tr>
        <tr><td>临沂</td><td>济南</td><td>45</td><td><button class="admin-btn" style="background:#f56c6c;">删除</button></td></tr>
      </tbody>
    </table>
  </div>

  <!-- 3. 查询可选日期配置 -->
  <div class="admin-section">
    <div class="section-title">📅 查询可选日期配置</div>
    <div class="admin-form-row">
      <div class="admin-form-label">可选日期</div>
      <input class="admin-form-input" placeholder="如：2025-07-01,2025-07-02" />
      <button class="admin-btn">保存</button>
    </div>
    <div class="admin-tip">当前可选日期：2025-07-01, 2025-07-02, 2025-07-03</div>
  </div>

  <!-- 4. 购票人信息查看与导出 -->
  <div class="admin-section">
    <div class="section-title">👥 购票人信息</div>
    <button class="admin-export-btn">导出Excel</button>
    <table class="admin-table">
      <thead>
        <tr><th>姓名</th><th>手机号</th><th>购票时间</th><th>出发地</th><th>目的地</th><th>日期</th></tr>
      </thead>
      <tbody>
        <tr><td>张三</td><td>138****8888</td><td>2025-07-01 10:23</td><td>济南</td><td>临沂</td><td>2025-07-01</td></tr>
        <tr><td>李四</td><td>139****6666</td><td>2025-07-02 14:12</td><td>临沂</td><td>济南</td><td>2025-07-02</td></tr>
      </tbody>
    </table>
  </div>

  <!-- 权限提示 -->
  <div class="admin-section" style="text-align:center;color:#f56c6c;background:#fffbe6;">
    <span style="font-size:16px;">⚠️ 仅管理员/开发者可见后台管理界面</span>
  </div>
</body>
</html> 