<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的</title>
    <link rel="stylesheet/less" type="text/css" href="styles.less" />
    <script src="https://cdn.jsdelivr.net/npm/less@4.1.1/dist/less.min.js"></script>
    <style>
        /* 我的页面特定样式 */
        .user-section {
            background: linear-gradient(135deg, @primary-color, #40a9ff);
            padding: 40px 20px 30px;
            color: white;
            text-align: center;
            position: relative;
        }
        
        .user-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        
        .user-content {
            position: relative;
            z-index: 1;
        }
        
        .welcome-text {
            font-size: 18px;
            margin-bottom: 20px;
            opacity: 0.9;
        }
        
        .login-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 32px;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .login-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .order-status {
            margin: -20px 16px 20px;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 16px;
        }
        
        .status-item {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .status-item:hover {
            transform: translateY(-2px);
        }
        
        .status-icon {
            width: 40px;
            height: 40px;
            background: @primary-color;
            border-radius: 50%;
            margin: 0 auto 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 18px;
        }
        
        .status-number {
            font-size: 18px;
            font-weight: bold;
            color: @text-color;
            margin-bottom: 4px;
        }
        
        .status-text {
            font-size: 12px;
            color: @text-secondary;
        }
        
        .function-grid {
            padding: 0 16px;
            margin-bottom: 20px;
        }
        
        .function-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 16px;
            color: @text-color;
        }
        
        .function-items {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr;
            gap: 16px;
        }
        
        .function-item {
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .function-item:hover {
            transform: translateY(-2px);
        }
        
        .function-icon {
            width: 48px;
            height: 48px;
            background: #f0f8ff;
            border-radius: 12px;
            margin: 0 auto 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: @primary-color;
            font-size: 24px;
        }
        
        .function-text {
            font-size: 12px;
            color: @text-color;
        }
        
        .promotion-card {
            margin: 0 16px 20px;
            background: linear-gradient(135deg, #ffecd2, #fcb69f);
            border-radius: 12px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            box-shadow: 0 4px 16px rgba(252, 182, 159, 0.3);
        }
        
        .promotion-content {
            color: #8b4513;
        }
        
        .promotion-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 4px;
        }
        
        .promotion-subtitle {
            font-size: 14px;
            opacity: 0.8;
        }
        
        .promotion-btn {
            background: #8b4513;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .promotion-btn:hover {
            background: #a0522d;
        }
        
        .footer-links {
            padding: 0 16px 20px;
        }
        
        .footer-link {
            display: block;
            padding: 12px 0;
            color: @text-secondary;
            text-decoration: none;
            border-bottom: 1px solid @border-color;
            font-size: 14px;
        }
        
        .footer-link:last-child {
            border-bottom: none;
        }
        
        .version-info {
            text-align: center;
            padding: 20px;
            color: @text-secondary;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 用户信息区 -->
        <div class="user-section">
            <div class="user-content">
                <div class="welcome-text">Hi,欢迎来到临校速通出行</div>
                <button class="login-btn">登录/注册</button>
            </div>
        </div>
        
        <!-- 订单状态卡片 -->
        <div class="order-status">
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-icon">🚌</div>
                    <div class="status-number">2</div>
                    <div class="status-text">待出行</div>
                </div>
                <div class="status-item">
                    <div class="status-icon">💰</div>
                    <div class="status-number">1</div>
                    <div class="status-text">待支付</div>
                </div>
                <div class="status-item">
                    <div class="status-icon">📋</div>
                    <div class="status-number">12</div>
                    <div class="status-text">历史订单</div>
                </div>
            </div>
        </div>
        
        <!-- 功能入口区 -->
        <div class="function-grid">
            <div class="function-title">常用功能</div>
            <div class="function-items">
                <div class="function-item">
                    <div class="function-icon">🎫</div>
                    <div class="function-text">代金券</div>
                </div>
                <div class="function-item">
                    <div class="function-icon">👤</div>
                    <div class="function-text">常用乘车人</div>
                </div>
                <div class="function-item">
                    <div class="function-icon">📤</div>
                    <div class="function-text">分享赚钱</div>
                </div>
                <div class="function-item">
                    <div class="function-icon">💬</div>
                    <div class="function-text">投诉建议</div>
                </div>
            </div>
        </div>
        
        <!-- 推广卡片 -->
        <div class="promotion-card">
            <div class="promotion-content">
                <div class="promotion-title">分享赚钱</div>
                <div class="promotion-subtitle">月入5000元</div>
            </div>
            <button class="promotion-btn">立即分享</button>
        </div>
        
        <!-- 其他信息 -->
        <div class="footer-links">
            <a href="#" class="footer-link">投诉建议</a>
            <a href="#" class="footer-link">做一样的小程序</a>
        </div>
        
        <div class="version-info">
            当前版本：v2.14.88
        </div>
        
        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <div class="icon"></div>
                <div class="text">首页</div>
            </div>
            <div class="tab-item active">
                <div class="icon"></div>
                <div class="text">我的</div>
            </div>
        </div>
    </div>
</body>
</html> 