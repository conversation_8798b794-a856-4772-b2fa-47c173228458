<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <link rel="stylesheet/less" type="text/css" href="styles.less" />
    <script src="https://cdn.jsdelivr.net/npm/less@4.1.1/dist/less.min.js"></script>
    <style>
        /* 首页特定样式 */
        .banner {
            height: 120px;
            background: linear-gradient(135deg, @primary-color, #40a9ff);
            border-radius: 0 0 20px 20px;
            margin-bottom: 20px;
            position: relative;
            overflow: hidden;
        }
        
        .banner::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
        }
        
        .banner-content {
            position: relative;
            z-index: 1;
            padding: 20px;
            color: white;
        }
        
        .banner-title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 8px;
        }
        
        .banner-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .search-section {
            padding: 0 16px;
            margin-bottom: 24px;
        }
        
        .search-form {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        }
        
        .route-input {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            padding: 12px;
            border: 1px solid @border-color;
            border-radius: 8px;
            background: #fafafa;
        }
        
        .route-input:last-child {
            margin-bottom: 0;
        }
        
        .route-label {
            width: 60px;
            font-size: 14px;
            color: @text-secondary;
        }
        
        .route-value {
            flex: 1;
            font-size: 16px;
            color: @text-color;
        }
        
        .swap-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: @primary-color;
            color: white;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 12px auto;
            transition: all 0.3s ease;
        }
        
        .swap-btn:hover {
            transform: rotate(180deg);
        }
        
        .search-btn {
            width: 100%;
            padding: 16px;
            background: @primary-color;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 18px;
            font-weight: bold;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
            transition: all 0.3s ease;
        }
        
        .search-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
        }
        
        .common-routes {
            padding: 0 16px;
            margin-bottom: 24px;
        }
        
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 16px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            color: @text-color;
        }
        
        .clear-link {
            font-size: 14px;
            color: @primary-color;
            text-decoration: none;
        }
        
        .route-tabs {
            display: flex;
            gap: 12px;
            margin-bottom: 16px;
            overflow-x: auto;
            padding-bottom: 8px;
        }
        
        .route-tab {
            padding: 8px 16px;
            background: #f0f0f0;
            border-radius: 20px;
            font-size: 14px;
            color: @text-secondary;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .route-tab.active {
            background: @primary-color;
            color: white;
        }
        
        .route-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 12px;
        }
        
        .route-card {
            background: white;
            border-radius: 8px;
            padding: 16px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .route-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
        }
        
        .route-name {
            font-size: 16px;
            font-weight: 500;
            color: @text-color;
            margin-bottom: 4px;
        }
        
        .route-price {
            font-size: 14px;
            color: @primary-color;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 公告栏 -->
        <div class="banner">
            <div class="banner-content">
                <div class="banner-title">临校速通</div>
                <div class="banner-subtitle">校园出行购票平台</div>
            </div>
        </div>
        
        <!-- 搜索区域 -->
        <div class="search-section">
            <div class="search-form">
                <div class="route-input">
                    <div class="route-label">出发地</div>
                    <div class="route-value">临沂</div>
                </div>
                
                <button class="swap-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 10l5 5 5-5z"/>
                    </svg>
                </button>
                
                <div class="route-input">
                    <div class="route-label">目的地</div>
                    <div class="route-value">临沂各县城</div>
                </div>
                
                <button class="search-btn">查询</button>
            </div>
        </div>
        
        <!-- 常用线路区域 -->
        <div class="common-routes">
            <div class="section-header">
                <div class="section-title">常用线路</div>
                <a href="#" class="clear-link">清除</a>
            </div>
            
            <div class="route-tabs">
                <div class="route-tab active">济南-临沂各县城</div>
                <div class="route-tab">济南-烟台各县城</div>
                <div class="route-tab">济南-潍坊各↓</div>
            </div>
            
            <div class="route-grid">
                <div class="route-card">
                    <div class="route-name">济南至临沂</div>
                    <div class="route-price">¥45起</div>
                </div>
                <div class="route-card">
                    <div class="route-name">临沂至济南</div>
                    <div class="route-price">¥45起</div>
                </div>
                <div class="route-card">
                    <div class="route-name">济南至东营</div>
                    <div class="route-price">¥38起</div>
                </div>
                <div class="route-card">
                    <div class="route-name">东营至济南</div>
                    <div class="route-price">¥38起</div>
                </div>
                <div class="route-card">
                    <div class="route-name">济南至菏泽</div>
                    <div class="route-price">¥42起</div>
                </div>
                <div class="route-card">
                    <div class="route-name">菏泽至济南</div>
                    <div class="route-price">¥42起</div>
                </div>
            </div>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="tab-bar">
            <div class="tab-item active">
                <div class="icon"></div>
                <div class="text">首页</div>
            </div>
            <div class="tab-item">
                <div class="icon"></div>
                <div class="text">我的</div>
            </div>
        </div>
    </div>
</body>
</html> 