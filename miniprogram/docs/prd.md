# 临校速通小程序前端开发需求

## 设计目标
开发一个名为临校速通的校园出行购票微信小程序，方便学习学生直接使用小程序在线购票。包含首页和"我的"三大核心页面

## 整体要求
- 采用清新校园风格，主色调为蓝色
- 页面全部采用ts和less编写，UI组件库采用vant
- 响应式设计，适配不同手机尺寸
- 底部导航栏包含三个选项：首页、我的

---

## 首页页面需求

### 公告栏
位于页面最上方，采用轮播图的形式来展示最近的几条公告信息

### 搜索区域
- 出发地/目的地选择框：
  - 左侧标签："出发地"/"目的地"
  - 右侧默认值："临沂"/"临沂各县城"
  - 分隔线
- 交换按钮：圆形，带交换图标
- 查询按钮：大号蓝色按钮，带阴影

### 常用线路区域
- 标题："常用线路"
- 右侧"清除"链接
- 标签导航：可横向滚动
  - "济南-临沂各县城"
  - "济南-烟台各县城"
  - "济南-潍坊各↓" (带下拉图标)
- 常用线路卡片(2列布局)：
  - "济南至临沂"
  - "临沂至济南"
  - "济南至东营"
  - "东营至济南"
  - "济南至菏泽"
  - "菏泽至济南"

---

## "我的"页面需求

### 用户信息区
- "登录/注册"按钮
- 欢迎语："Hi,欢迎来到临校速通出行"
- 蓝色登录按钮

### 订单状态卡片
- 三列布局：
  - 待出行：图标 + 数字 + 文本
  - 待支付：图标 + 数字 + 文本
  - 历史订单：图标 + 数字 + 文本

### 功能入口区
- 网格布局(4列)：
  - 代金券(券图标)
  - 常用乘车人(用户图标)
  - 分享赚钱(分享图标 + "月入5000元")
  - 投诉建议(客服图标)

### 推广卡片
- 背景渐变：#ffecd2 到 #fcb69f
- 左侧文本："分享赚钱" + "月入5000元"
- 右侧按钮："立即分享"

### 其他信息
- "投诉建议"文本链接
- 版本信息："当前版本：v2.14.88"
- "做一样的小程序"链接

---

## 全局组件需求

### 底部导航栏
- 固定在底部
- 三个选项：
  - 首页(首页图标)
  - 我的(用户图标)
- 当前选中状态为蓝色

### 交互效果
- 按钮点击态效果
- 卡片悬停效果
- 标签页切换效果
- 输入框获取焦点效果

## 技术实现建议
- 使用微信小程序原生框架
- 采用rpx单位实现响应式
- 使用Flex布局
- 图标使用微信小程序内置图标或iconfont