import UserService from './utils/userService';

// app.ts
interface IAppOption {
  globalData: {
    userService: UserService;
    userInfo: UserInfo | null;
  };
  initCloud(): void;
  checkUserLoginStatus(): Promise<void>;
  getUserService(): UserService;
  getUserInfo(): UserInfo | null;
  setUserInfo(userInfo: UserInfo): void;
}

App<IAppOption>({
  globalData: {
    userService: new UserService(),
    userInfo: null
  },

  onLaunch() {
    console.log('小程序启动');
    
    // 初始化云开发
    this.initCloud();
    
    // 检查用户登录状态
    this.checkUserLoginStatus();
  },

  onShow() {
    console.log('小程序显示');
  },

  onHide() {
    console.log('小程序隐藏');
  },

  // 初始化云开发
  initCloud() {
    try {
      const cloudService = require('./utils/cloud').default.getInstance();
      console.log('云开发初始化成功');
    } catch (error) {
      console.error('云开发初始化失败:', error);
    }
  },

  // 检查用户登录状态
  async checkUserLoginStatus() {
    try {
      const userService = this.globalData.userService;
      const userInfo = await userService.getUserInfo();
      
      if (userInfo) {
        this.globalData.userInfo = userInfo;
        console.log('用户已登录:', userInfo.nickName);
      } else {
        console.log('用户未登录');
      }
    } catch (error) {
      console.error('检查登录状态失败:', error);
    }
  },

  // 获取用户服务实例
  getUserService(): UserService {
    return this.globalData.userService;
  },

  // 获取用户信息
  getUserInfo(): UserInfo | null {
    return this.globalData.userInfo;
  },

  // 设置用户信息
  setUserInfo(userInfo: UserInfo) {
    this.globalData.userInfo = userInfo;
  }
});