import CloudService from './cloud';

// 用户服务类
class UserService {
  private cloudService: CloudService;
  private userCollection: any;

  constructor() {
    this.cloudService = CloudService.getInstance();
    this.userCollection = this.cloudService.getCollection('users');
  }

  // 微信登录
  public async wxLogin(): Promise<LoginResult> {
    try {
      // 获取微信登录凭证
      const loginResult = await new Promise<any>((resolve, reject) => {
        wx.login({
          success: resolve,
          fail: reject
        });
      });
      
      if (loginResult.code) {
        // 调用云函数进行登录
        const result = await this.cloudService.callFunction('login', {
          code: loginResult.code
        });
        
        // 登录成功后，自动 upsert 用户文档，补全 openid、nickName、avatarUrl 字段
        if (result.success && result.userInfo && result.userInfo.openid) {
          await this.upsertUserProfile(result.userInfo);
        }
        
        return result;
      } else {
        return {
          success: false,
          message: '微信登录失败'
        };
      }
    } catch (error) {
      console.error('登录失败:', error);
      return {
        success: false,
        message: '登录失败，请重试'
      };
    }
  }

  // 获取用户信息
  public async getUserInfo(): Promise<UserInfo | null> {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      if (userInfo) {
        return userInfo;
      }
      return null;
    } catch (error) {
      console.error('获取用户信息失败:', error);
      return null;
    }
  }

  // 更新用户信息
  public async updateUserInfo(userInfo: Partial<UserInfo>): Promise<boolean> {
    try {
      const currentUser = await this.getUserInfo();
      if (!currentUser) {
        wx.showToast({ title: '用户未登录', icon: 'none' });
        return false;
      }

      const updateData = {
        ...userInfo,
        updateTime: new Date()
      };

      // 更新云数据库
      await this.updateUserInCloud(currentUser.openid, updateData);

      // 主动拉取最新数据
      const db = this.cloudService.getDatabase();
      const res = await db.collection('users').where({ openid: currentUser.openid }).get();
      if (res.data && res.data.length > 0) {
        wx.setStorageSync('userInfo', res.data[0]);
        // 同步全局 userInfo
        const app = getApp();
        if (app.setUserInfo) app.setUserInfo(res.data[0]);
      } else {
        wx.showToast({ title: '用户不存在或未注册', icon: 'none' });
        return false;
      }

      return true;
    } catch (error) {
      wx.showToast({ title: '更新用户信息失败', icon: 'none' });
      console.error('更新用户信息失败:', error);
      return false;
    }
  }

  // 检查用户是否已登录
  public isLoggedIn(): boolean {
    try {
      const userInfo = wx.getStorageSync('userInfo');
      return !!userInfo;
    } catch (error) {
      return false;
    }
  }

  // 退出登录
  public logout(): void {
    try {
      wx.removeStorageSync('userInfo');
      wx.removeStorageSync('openid');
    } catch (error) {
      console.error('退出登录失败:', error);
    }
  }

  // 强制从云端拉取用户信息
  public async fetchUserInfoFromCloud(): Promise<UserInfo | null> {
    try {
      const currentUser = wx.getStorageSync('userInfo');
      console.log('[fetchUserInfoFromCloud] 本地 userInfo:', currentUser);
      if (!currentUser || !currentUser.openid) return null;
      const db = this.cloudService.getDatabase();
      console.log('[fetchUserInfoFromCloud] 查询 openid:', currentUser.openid);
      const res = await db.collection('users').where({ openid: currentUser.openid }).get();
      if (res.data && res.data.length > 0) {
        wx.setStorageSync('userInfo', res.data[0]);
        return res.data[0];
      }
      return null;
    } catch (error) {
      console.error('云端拉取用户信息失败:', error);
      return null;
    }
  }

  // 在云数据库中更新用户信息
  private async updateUserInCloud(openid: string, updateData: any): Promise<void> {
    // 保证 updateData 不包含 _.set，顶层字段直接赋值
    const cleanUpdateData = { ...updateData };
    // 递归移除所有 _.set
    Object.keys(cleanUpdateData).forEach(key => {
      if (cleanUpdateData[key] && cleanUpdateData[key].constructor && cleanUpdateData[key].constructor.name === 'UpdateCommand') {
        // 这里假设 _.set 只会用于嵌套对象，顶层字段直接赋值
        cleanUpdateData[key] = cleanUpdateData[key].value || cleanUpdateData[key];
      }
    });
    console.log('[updateUserInCloud] 最终 updateData:', cleanUpdateData);
    return new Promise((resolve, reject) => {
      this.userCollection.where({
        openid: openid
      }).update({
        data: cleanUpdateData,
        success: (res: any) => {
          console.log('updateUserInCloud result:', res);
          if (res.stats && res.stats.matched === 0) {
            wx.showToast({ title: '数据库无此用户', icon: 'none' });
            reject(new Error('数据库无此用户'));
          } else {
            if (res.stats && res.stats.updated === 0) {
              wx.showToast({ title: '内容未变化', icon: 'none' });
            }
            resolve();
          }
        },
        fail: (err: any) => {
          wx.showToast({ title: '数据库更新失败', icon: 'none' });
          console.error('更新云数据库失败:', err);
          reject(err);
        }
      });
    });
  }

  // 自动 upsert 用户文档，确保 openid、nickName、avatarUrl 字段存在
  private async upsertUserProfile(userInfo: any): Promise<void> {
    const db = this.cloudService.getDatabase();
    const openid = userInfo.openid;
    const nickName = userInfo.nickName || '';
    const avatarUrl = userInfo.avatarUrl || '';
    // 查询是否已存在
    const res = await db.collection('users').where({ openid }).get();
    if (res.data && res.data.length > 0) {
      // 已存在，补字段（如果没有）
      const doc = res.data[0];
      const updateData: any = {};
      if (!('nickName' in doc)) updateData.nickName = nickName;
      if (!('avatarUrl' in doc)) updateData.avatarUrl = avatarUrl;
      if (Object.keys(updateData).length > 0) {
        await db.collection('users').where({ openid }).update({ data: updateData });
      }
    } else {
      // 不存在，插入新文档
      await db.collection('users').add({
        data: {
          openid,
          nickName,
          avatarUrl,
          createTime: new Date(),
          updateTime: new Date()
        }
      });
    }
  }

  // 保存用户信息到本地
  public saveUserInfo(userInfo: UserInfo): void {
    try {
      wx.setStorageSync('userInfo', userInfo);
      wx.setStorageSync('openid', userInfo.openid);
    } catch (error) {
      console.error('保存用户信息失败:', error);
    }
  }
}

export default UserService; 