// 云开发工具类
class CloudService {
  private static instance: CloudService;
  private cloud: any;

  private constructor() {
    this.cloud = wx.cloud;
    this.initCloud();
  }

  public static getInstance(): CloudService {
    if (!CloudService.instance) {
      CloudService.instance = new CloudService();
    }
    return CloudService.instance;
  }

  // 初始化云开发
  private initCloud(): void {
    this.cloud.init({
      env: 'cloud1-5g83xj46e99cc3de',
      traceUser: true
    });
  }

  // 调用云函数
  public callFunction(name: string, data?: any): Promise<any> {
    return new Promise((resolve, reject) => {
      this.cloud.callFunction({
        name,
        data,
        success: (res: any) => {
          resolve(res.result);
        },
        fail: (err: any) => {
          console.error('云函数调用失败:', err);
          reject(err);
        }
      });
    });
  }

  // 获取数据库实例
  public getDatabase(): any {
    return this.cloud.database();
  }

  // 获取集合
  public getCollection(name: string): any {
    return this.getDatabase().collection(name);
  }

  // 上传文件到云存储
  public uploadFile(cloudPath: string, filePath: string): Promise<any> {
    return new Promise((resolve, reject) => {
      this.cloud.uploadFile({
        cloudPath,
        filePath,
        success: (res: any) => resolve(res),
        fail: (err: any) => reject(err)
      });
    });
  }
}

export default CloudService; 