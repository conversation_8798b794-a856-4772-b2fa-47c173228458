# 临校速通小程序

## 项目简介

临校速通是一个基于微信小程序平台的校园出行服务平台，为学生提供便捷的校际交通服务。

## 技术栈

- **框架**: 微信小程序原生框架
- **语言**: TypeScript
- **样式**: Less
- **UI组件**: Vant Weapp
- **开发工具**: 微信开发者工具

## 项目结构

```
miniprogram/
├── app.ts                 # 小程序入口文件
├── app.json              # 小程序配置文件
├── app.less              # 全局样式文件
├── pages/                # 页面目录
│   ├── home/             # 首页
│   │   ├── home.ts       # 页面逻辑
│   │   ├── home.wxml     # 页面结构
│   │   ├── home.less     # 页面样式
│   │   └── home.json     # 页面配置
│   └── profile/          # 我的页面
│       ├── profile.ts    # 页面逻辑
│       ├── profile.wxml  # 页面结构
│       ├── profile.less  # 页面样式
│       └── profile.json  # 页面配置
├── components/           # 自定义组件
│   └── navigation-bar/   # 导航栏组件
├── utils/                # 工具函数
├── images/               # 图片资源
└── typings/              # TypeScript类型定义
```

## 主要功能

### 首页功能
- 出发地和目的地选择
- 线路查询
- 常用线路管理
- 线路分类展示

### 我的页面功能
- 用户登录/注册
- 订单状态查看
- 常用功能入口
- 分享赚钱
- 投诉建议

## 开发说明

### TypeScript 使用

项目完全使用 TypeScript 进行开发，提供更好的类型安全和开发体验：

1. **接口定义**: 为数据结构定义接口
   ```typescript
   interface RouteItem {
     id: number;
     name: string;
     price: number;
   }
   ```

2. **类型注解**: 为函数参数和返回值添加类型
   ```typescript
   onRouteSelect(event: WechatMiniprogram.TouchEvent) {
     const route = event.currentTarget.dataset.route as RouteItem;
   }
   ```

3. **类型断言**: 使用 `as` 关键字进行类型转换
   ```typescript
   const userInfo = wx.getStorageSync('userInfo') as WechatMiniprogram.UserInfo;
   ```

### 导航栏配置

使用微信原生导航栏，配置简洁：

```json
{
  "navigationBarTitleText": "临校速通",
  "backgroundColor": "#f8f9fa"
}
```

### 组件使用

使用 Vant Weapp 组件库：

```json
{
  "usingComponents": {
    "van-button": "@vant/weapp/button/index",
    "van-cell": "@vant/weapp/cell/index"
  }
}
```

## 开发环境

1. 安装依赖：
   ```bash
   npm install
   ```

2. 构建 NPM：
   在微信开发者工具中点击"构建 npm"

3. 编译项目：
   微信开发者工具会自动编译 TypeScript 文件

## 注意事项

- 确保微信开发者工具已开启 TypeScript 编译
- 修改代码后会自动重新编译
- 使用 `wx.` API 时会有完整的类型提示
- 组件事件处理函数需要正确的类型注解

## 版本信息

- 当前版本：v2.14.88
- 微信小程序基础库：2.32.3
- Vant Weapp：1.11.7 