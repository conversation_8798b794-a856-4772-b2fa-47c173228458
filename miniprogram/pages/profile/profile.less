/* 我的页面特定样式 */
.user-section {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  padding: 40px 20px 30px;
  color: white;
  position: relative;
  overflow: hidden;
}

.user-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="rgba(255,255,255,0.1)"/><circle cx="80" cy="40" r="1.5" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="80" r="1" fill="rgba(255,255,255,0.1)"/></svg>');
}

.user-content {
  position: relative;
  z-index: 1;
}

.welcome-text {
  font-size: 18px;
  margin-bottom: 20px;
  opacity: 0.9;
  text-align: center;
}

.login-btn {
  background: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  color: white !important;
  padding: 12px 32px !important;
  border-radius: 25px !important;
  font-size: 16px !important;
  display: block !important;
  margin: 0 auto !important;
}

.login-btn:active {
  background: rgba(255, 255, 255, 0.3) !important;
}

/* 用户信息显示 */
.user-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 2px solid rgba(255, 255, 255, 0.3);
  flex-shrink: 0;
}

.user-details {
  flex: 1;
  margin-left: 15px;
  margin-right: 15px;
}

.user-name {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-phone {
  font-size: 14px;
  opacity: 0.8;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.user-actions {
  flex-shrink: 0;
}

.settings-btn {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(0,0,0,0.06); // 临时调试背景，便于观察点击区域
}

.settings-btn:active {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(0.95);
}

/* 设置菜单弹窗 */
.settings-overlay {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  display: flex;
  align-items: flex-start;
  justify-content: flex-end;
  padding: 80px 20px 0;
}

.settings-menu {
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  min-width: 160px;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.menu-item:active {
  background: #f5f5f5;
}

.menu-text {
  flex: 1;
  margin-left: 12px;
  font-size: 16px;
  color: #333;
}

.menu-text-danger {
  color: #ff4d4f;
}

.menu-divider {
  height: 1px;
  background: #f0f0f0;
  margin: 0 20px;
}

/* 订单状态卡片 */
.order-status {
  margin: -20px 16px 20px;
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}

/* 状态网格布局 */
.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 16px;
}

.status-item {
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 80px;
}

.status-item:active {
  transform: translateY(-2px);
}

.status-icon {
  width: 40px;
  height: 40px;
  background: #1890ff;
  border-radius: 50%;
  margin: 0 auto 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
}

.status-number {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
  display: block;
}

.status-text {
  font-size: 12px;
  color: #666;
  display: block;
}

.container {
  padding: 0 16px;
  margin-bottom: 16px;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
  padding: 0 4px;
}

/* 功能列表布局 */
.function-list {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.function-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: all 0.3s ease;
}

.function-item:last-child {
  border-bottom: none;
}

.function-item:active {
  background: #f8f9fa;
}

.function-icon {
  width: 40px;
  height: 40px;
  background: #f0f8ff;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  font-size: 20px;
  margin-right: 12px;
  flex-shrink: 0;
}

.function-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.function-text {
  font-size: 16px;
  color: #333;
  font-weight: 500;
  margin-bottom: 2px;
}

.function-desc {
  font-size: 12px;
  color: #999;
}

.function-arrow {
  color: #ccc;
  font-size: 16px;
  font-weight: bold;
}

.promotion-card {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4px 16px rgba(252, 182, 159, 0.3);
  margin-bottom: 16px;
}

.promotion-content {
  color: #8b4513;
}

.promotion-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.promotion-subtitle {
  font-size: 14px;
  opacity: 0.8;
}

.promotion-btn {
  background: #8b4513 !important;
  color: white !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 20px !important;
  font-size: 14px !important;
}

.promotion-btn:active {
  background: #a0522d !important;
}

.version-info {
  text-align: center;
  padding: 20px;
  color: #666;
  font-size: 12px;
}

// 编辑个人信息弹窗优化
.edit-profile-modal {
  background: #fff;
  border-radius: 22px;
  box-shadow: 0 8px 32px rgba(0,0,0,0.12);
  width: 82vw;
  max-width: 360px;
  padding: 24px 20px 48px 20px; // 底部内边距加大，避免内容被按钮遮挡
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}
.modal-header {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}
.modal-title {
  font-size: 18px;
  font-weight: 600;
  color: #222;
}
.modal-close {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background 0.2s;
}
.modal-close:active {
  background: #f2f2f2;
}
.modal-avatar-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 18px;
}
.modal-avatar {
  width: 76px;
  height: 76px;
  border-radius: 50%;
  border: 2px solid #e6e6e6;
  background: #f7f7f7;
  object-fit: cover;
}
.modal-avatar-tip {
  font-size: 12px;
  color: #aaa;
  margin-top: 6px;
}
.modal-form-item {
  width: 100%;
  margin-bottom: 22px;
  box-sizing: border-box;
}
.modal-input {
  width: 100%;
  box-sizing: border-box;
  height: 38px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  padding: 0 12px;
  font-size: 15px;
  background: #fafbfc;
  color: #222;
}
.modal-btn-group.single {
  position: absolute;
  right: 20px;
  bottom: 20px;
  width: auto;
  display: flex;
  justify-content: flex-end;
}
.modal-btn.confirm {
  background: #1890ff;
  color: #fff;
  border: none;
  width: 120px;
  height: 38px;
  border-radius: 8px;
  font-size: 15px;
  font-weight: 500;
} 

.modal-overlay {
  position: fixed;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.08);
  z-index: 1200;
  display: flex;
  align-items: center;
  justify-content: center;
} 

.modal-label {
  font-size: 14px;
  color: #333;
  margin-bottom: 6px;
  font-weight: 500;
  display: flex;
  align-items: center;
}
.required {
  color: #ff4d4f;
  margin-left: 2px;
  font-size: 15px;
} 