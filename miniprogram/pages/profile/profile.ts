
import CloudService from '../../utils/cloud';

// 获取应用实例
const app = getApp() as IAppOption;

Page({
  data: {
    userInfo: null as UserInfo | null,
    isLoggedIn: false,
    showSettings: false,
    // 新增弹窗相关data
    showEditProfileModal: false,
    editProfileAvatar: '',
    editProfileNickname: '',
    orderStats: {
      pending: 2,
      unpaid: 1,
      history: 12
    }
  },

  onLoad() {
    console.log('我的页面加载完成');
    this.checkUserInfo();
    
    // 动态设置导航栏标题
    wx.setNavigationBarTitle({
      title: '我的'
    });
    
    // 动态设置导航栏背景色为白色
    wx.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff'
    });
  },

  onShow() {
    console.log('我的页面显示，当前数据:', this.data);
    this.checkUserInfo();
  },

  // 检查用户信息
  async checkUserInfo() {
    try {
      const userService = app.getUserService();
      const isLoggedIn = userService.isLoggedIn();
      let userInfo = await userService.getUserInfo();

      // 如果已登录，强制从云端拉取一次最新数据
      if (isLoggedIn && userInfo && userInfo.openid) {
        userInfo = await userService.fetchUserInfoFromCloud();
      }
      
      this.setData({
        isLoggedIn,
        userInfo
      });
      console.log('用户登录状态:', isLoggedIn);
    } catch (error) {
      console.error('检查用户信息失败:', error);
    }
  },

  // 登录/注册
  async onLogin() {
    try {
      wx.showLoading({
        title: '登录中...'
      });

      const userService = app.getUserService();
      const loginResult = await userService.wxLogin();
      
      wx.hideLoading();
      
      if (loginResult.success && loginResult.userInfo) {
        // 保存用户信息
        userService.saveUserInfo(loginResult.userInfo);
        app.setUserInfo(loginResult.userInfo);
        
        this.setData({
          isLoggedIn: true,
          userInfo: loginResult.userInfo
        });
        
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        });
        
        console.log('登录成功:', loginResult.userInfo);
      } else {
        wx.showToast({
          title: loginResult.message || '登录失败',
          icon: 'none'
        });
      }
    } catch (error) {
      wx.hideLoading();
      wx.showToast({
        title: '登录失败，请重试',
        icon: 'none'
      });
      console.error('登录失败:', error);
    }
  },

  // 显示设置菜单
  onShowSettings() {
    this.setData({
      showSettings: true
    });
  },

  // 隐藏设置菜单
  onHideSettings() {
    console.log('遮罩被点击，准备隐藏菜单');
    this.setData({
      showSettings: false
    });
  },

  // 菜单点击事件（阻止冒泡）
  onMenuClick() {
    // 阻止事件冒泡
  },

  // 修改信息，弹出modal
  onEditProfile() {
    let avatar = this.data.userInfo?.avatarUrl || '';
    if (avatar === 'local_default') {
      avatar = '/images/default.jpeg';
    }
    this.setData({
      showSettings: false,
      showEditProfileModal: true,
      editProfileAvatar: avatar,
      editProfileNickname: this.data.userInfo?.nickName || '',
    });
  },

  // 关闭编辑弹窗
  onHideEditProfileModal() {
    this.setData({ showEditProfileModal: false });
  },

  // 更换头像
  async onEditAvatar() {
    const that = this;
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success(res) {
        if (res.tempFilePaths && res.tempFilePaths.length > 0) {
          that.setData({ editProfileAvatar: res.tempFilePaths[0] });
        }
      }
    });
  },

  // 昵称输入
  onNicknameInput(e: any) {
    this.setData({ editProfileNickname: e.detail.value });
  },

  // 保存编辑
  async onSaveEditProfile() {
    const { editProfileAvatar, editProfileNickname, userInfo } = this.data;
    if (!editProfileNickname.trim()) {
      wx.showToast({ title: '请输入昵称', icon: 'none' });
      return;
    }
    if (!userInfo) {
      wx.showToast({ title: '用户信息异常', icon: 'none' });
      return;
    }
    const userService = app.getUserService();
    let avatarUrl = userInfo.avatarUrl;
    // 判断头像是否有变更（本地路径）
    if (editProfileAvatar && editProfileAvatar !== userInfo.avatarUrl && !editProfileAvatar.startsWith('cloud://')) {
      wx.showLoading({ title: '上传头像...' });
      try {
        const cloudPath = `avatar/${userInfo.openid}_${Date.now()}.jpg`;
        const cloudService = CloudService.getInstance();
        const uploadRes = await cloudService.uploadFile(cloudPath, editProfileAvatar);
        avatarUrl = uploadRes.fileID;
      } catch (e) {
        wx.hideLoading();
        wx.showToast({ title: '头像上传失败', icon: 'none' });
        return;
      }
      wx.hideLoading();
    }
    // 更新云数据库和本地
    const updateRes = await userService.updateUserInfo({
      avatarUrl,
      nickName: editProfileNickname
    });
    if (updateRes) {
      this.setData({
        'userInfo.avatarUrl': avatarUrl,
        'userInfo.nickName': editProfileNickname,
        showEditProfileModal: false
      });
      wx.showToast({ title: '保存成功', icon: 'success' });
    } else {
      wx.showToast({ title: '保存失败', icon: 'none' });
    }
  },

  // 退出登录
  onLogout() {
    this.setData({
      showSettings: false
    });
    wx.showModal({
      title: '退出登录',
      content: '确定要退出登录吗？',
      confirmText: '退出',
      confirmColor: '#ff4d4f',
      success: (res) => {
        if (res.confirm) {
          const userService = app.getUserService();
          userService.logout();
          this.setData({
            isLoggedIn: false,
            userInfo: null
          });
          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  },

  // 订单状态点击
  onOrderStatus(event: any) {
    const type = event.currentTarget.dataset.type;
    console.log('点击订单状态:', type);
    wx.showToast({
      title: `查看${type === 'pending' ? '待出行' : type === 'unpaid' ? '待支付' : '历史'}订单`,
      icon: 'none'
    });
  },

  // 功能点击
  onFunction(event: any) {
    const type = event.currentTarget.dataset.type;
    console.log('点击功能:', type);
    const functionNames = {
      coupon: '代金券',
      passenger: '常用乘车人',
      feedback: '投诉建议'
    };
    wx.showToast({
      title: `进入${functionNames[type as keyof typeof functionNames] || '功能'}`,
      icon: 'none'
    });
  }
}); 