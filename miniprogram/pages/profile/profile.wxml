<!-- 用户信息区 -->
<view class="user-section">
  <view class="user-content">
    <block wx:if="{{isLoggedIn && userInfo}}">
      <view class="user-info">
        <image class="user-avatar" src="{{userInfo.avatarUrl === 'local_default' ? '/images/default.jpeg' : userInfo.avatarUrl || '/images/profile.png'}}" mode="aspectFill"></image>
        <view class="user-details">
          <view class="user-name">{{userInfo.nickName || '用户'}}</view>
          <view class="user-phone" wx:if="{{userInfo.phone}}">{{userInfo.phone}}</view>
          <view class="user-phone" wx:else>Hi,欢迎来到临校速通</view>
        </view>
        <view class="user-actions">
          <view class="settings-btn" bind:click="onShowSettings">
            <van-icon name="setting-o" size="20" color="#ffffff" bind:click="onShowSettings" />
          </view>
        </view>
      </view>
    </block>
    <block wx:else>
      <view class="welcome-text">Hi,欢迎来到临校速通</view>
      <van-button 
        type="default" 
        size="normal" 
        round 
        bind:click="onLogin"
        custom-class="login-btn"
      >
        登录/注册
      </van-button>
    </block>
  </view>
</view>

<!-- 设置菜单弹窗 -->
<view wx:if="{{showSettings}}">
  <view class="settings-overlay" bindtap="onHideSettings">
    <view class="settings-menu" catchtap="onMenuClick">
      <!-- 菜单内容 -->
      <view class="menu-item" bindtap="onEditProfile">
        <text class="menu-text">修改信息</text>
      </view>
      <view class="menu-divider"></view>
      <view class="menu-item" bindtap="onLogout">
        <text class="menu-text menu-text-danger">退出登录</text>
      </view>
    </view>
  </view>
</view>

<!-- 订单状态卡片 -->
<view class="order-status">
  <view class="status-grid">
    <view class="status-item" bind:click="onOrderStatus" data-type="pending">
      <view class="status-icon">🚌</view>
      <view class="status-number">{{ orderStats.pending }}</view>
      <view class="status-text">待出行</view>
    </view>
    <view class="status-item" bind:click="onOrderStatus" data-type="unpaid">
      <view class="status-icon">💰</view>
      <view class="status-number">{{ orderStats.unpaid }}</view>
      <view class="status-text">待支付</view>
    </view>
    <view class="status-item" bind:click="onOrderStatus" data-type="history">
      <view class="status-icon">📋</view>
      <view class="status-number">{{ orderStats.history }}</view>
      <view class="status-text">历史订单</view>
    </view>
  </view>
</view>

<!-- 功能入口区 -->
<view class="container">
  <view class="section-title">常用功能</view>
  <view class="function-list">
    <view class="function-item" bind:click="onFunction" data-type="coupon">
      <view class="function-icon">🎫</view>
      <view class="function-content">
        <view class="function-text">代金券</view>
        <view class="function-desc">查看我的优惠券</view>
      </view>
      <view class="function-arrow">></view>
    </view>
    <view class="function-item" bind:click="onFunction" data-type="passenger">
      <view class="function-icon">👤</view>
      <view class="function-content">
        <view class="function-text">常用乘车人</view>
        <view class="function-desc">管理乘车人信息</view>
      </view>
      <view class="function-arrow">></view>
    </view>
    <view class="function-item" bind:click="onFunction" data-type="feedback">
      <view class="function-icon">💬</view>
      <view class="function-content">
        <view class="function-text">投诉建议</view>
        <view class="function-desc">反馈问题与建议</view>
      </view>
      <view class="function-arrow">></view>
    </view>
  </view>
</view>

<!-- 其他信息 -->
<view class="container">
  <view class="version-info">
    当前版本：v2.14.88
  </view>
</view> 

<!-- 编辑个人信息弹窗 -->
<view wx:if="{{showEditProfileModal}}">
  <view class="modal-overlay" bindtap="onHideEditProfileModal">
    <view class="edit-profile-modal" catchtap="noop">
      <view class="modal-header">
        <view class="modal-title">编辑个人信息</view>
        <van-icon name="cross" size="22" color="#bbb" class="modal-close" bindtap="onHideEditProfileModal" />
      </view>
      <view class="modal-avatar-section">
        <image class="modal-avatar" src="{{editProfileAvatar || userInfo.avatarUrl}}" mode="aspectFill" bindtap="onEditAvatar" />
        <view class="modal-avatar-tip">点击更换头像</view>
      </view>
      <view class="modal-form-item">
        <view class="modal-label">昵称<text class="required">*</text></view>
        <input class="modal-input" placeholder="请输入昵称" value="{{editProfileNickname}}" bindinput="onNicknameInput" maxlength="16" />
      </view>
      <view class="modal-btn-group single">
        <button class="modal-btn confirm" bindtap="onSaveEditProfile">保存</button>
      </view>
    </view>
  </view>
</view> 