/* 轮播图样式 */
.carousel-section {
  margin-bottom: 16px;
}

.carousel {
  height: 200px;
  border-radius: 0;
  overflow: hidden;
  margin: 0;
  width: 100%;
}

.carousel-item {
  position: relative;
  width: 100%;
  height: 100%;
}

.carousel-image {
  width: 100%;
  height: 100%;
}

.carousel-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
  padding: 20px 16px 16px;
  color: white;
}

.carousel-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.carousel-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* 首页特定样式 */
.container {
  padding: 16px;
}

.card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 路线输入区域布局 */
.route-input-section {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.route-inputs {
  flex: 1;
}

.route-input-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.route-input-item:last-child {
  border-bottom: none;
}

.route-label {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.route-value {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #666;
  font-size: 14px;
}

.swap-button-container {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  height: 100%;
}

/* 圆形转换按钮样式 */
.swap-btn {
  background: #4a90e2;
  color: white;
  border: none;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(74, 144, 226, 0.3);
  transition: all 0.2s ease;
}

.swap-btn:active {
  background: #357abd;
  transform: scale(0.95);
  box-shadow: 0 1px 2px rgba(74, 144, 226, 0.4);
}

/* 转换图标样式 */
.swap-icon {
  width: 20px;
  height: 20px;
  filter: brightness(0) invert(1); /* 将图标转换为白色 */
}

/* 常用线路标题样式 */
.section-header {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
  background: #EFEFEF;
  padding: 12px 16px;
  border-radius: 8px;
}

.section-line {
  width: 40px;
  height: 1px;
  background: #CCCCCC;
  margin: 0 12px;
}

.section-title-text {
  font-size: 14px;
  color: #BBBBBB;
  font-weight: 400;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 路线网格布局 */
.route-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  padding: 16px 0;
}

.route-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-height: 80px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.route-card:active {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.route-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  line-height: 1.2;
}

.route-price {
  font-size: 14px;
  color: #1890ff;
  font-weight: bold;
}

.custom-btn {
  width: 80vw;
  height: 88rpx;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 44rpx;
  background: #1989fa;
  color: #fff;
  box-shadow: 0 4rpx 16rpx rgba(25,137,250,0.08);
  margin: 0 auto;
}

.custom-btn:active {
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4) !important;
}

/deep/ .custom-btn {
  width: 80vw !important;
  height: 88rpx !important;
  font-size: 36rpx !important;
  font-weight: bold !important;
  border-radius: 44rpx !important;
  background: #1989fa !important;
  color: #fff !important;
  box-shadow: 0 4rpx 16rpx rgba(25,137,250,0.08) !important;
  margin: 0 auto !important;
  display: block !important;
}

.search-section {
  display: flex;
  justify-content: center;
  margin: 32rpx 0;
}
.search-btn {
  background: #1989fa;
  color: #fff;
  border-radius: 24rpx;
  padding: 16rpx 48rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 4rpx 16rpx rgba(25,137,250,0.08);
}

/* 禁用日期置灰 */
.disabled-day {
  color: #ccc !important;
  background: #f5f5f5 !important;
  opacity: 0.5 !important;
  pointer-events: none !important;
}

/* 确保禁用日期不可点击 */
.van-calendar__day--disabled {
  color: #ccc !important;
  background: #f5f5f5 !important;
  opacity: 0.5 !important;
  pointer-events: none !important;
}

/* 强制所有日期都显示为禁用状态 */
.van-calendar__day {
  color: #ccc !important;
  background: #f5f5f5 !important;
  opacity: 0.5 !important;
  pointer-events: none !important;
}

/* 确保选中状态也被禁用 */
.van-calendar__day--selected {
  color: #ccc !important;
  background: #f5f5f5 !important;
  opacity: 0.5 !important;
  pointer-events: none !important;
} 