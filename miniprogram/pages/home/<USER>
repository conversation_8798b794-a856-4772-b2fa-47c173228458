 
<!-- 轮播图区域 -->
<view class="carousel-section">
  <swiper class="carousel" indicator-dots="{{ true }}" autoplay="{{ true }}" interval="{{ 3000 }}" circular="{{ true }}">
    <swiper-item wx:for="{{ carouselData }}" wx:key="id">
      <view class="carousel-item">
        <image src="{{ item.image }}" mode="aspectFill" class="carousel-image" />
        <view class="carousel-content">
          <view class="carousel-title">{{ item.title }}</view>
          <view class="carousel-subtitle">{{ item.subtitle }}</view>
        </view>
      </view>
    </swiper-item>
  </swiper>
</view>

<!-- 搜索区域 -->
<view class="container">
  <view class="card">
    <view class="route-input-section">
      <view class="route-inputs">
        <view class="route-input-item">
          <text class="route-label">出发地</text>
          <view class="route-value">
            <text>{{ defaultFrom }}</text>
            <van-icon name="arrow" />
          </view>
        </view>
        <view class="route-input-item">
          <text class="route-label">目的地</text>
          <view class="route-value">
            <text>{{ defaultTo }}</text>
            <van-icon name="arrow" />
          </view>
        </view>
      </view>
      <view class="swap-button-container">
        <view class="swap-btn" bind:tap="onSwapRoute">
          <image src="/images/arrow-sync.png" class="swap-icon" mode="aspectFit" />
        </view>
      </view>
    </view>
    
    <view class="search-section">
      <van-button type="primary" size="large" block bind:click="onShowCalendar" custom-class="custom-btn">查询</van-button>
    </view>
    <van-calendar
      show="{{ showCalendar }}"
      min-date="{{ minDate }}"
      max-date="{{ maxDate }}"
      color="#1989fa"
      type="single"
      formatter="{{ formatter }}"
      allow-same-day="{{ false }}"
      show-confirm="{{ false }}"
      poppable="{{ true }}"
      round="{{ false }}"
      bind:close="onCloseCalendar"
      bind:select="onSelectDate"
      bind:dayclick="onDayClick"
    />
  </view>

  <!-- 常用线路区域 -->
  <view class="section-header">
    <view class="section-line"></view>
    <text class="section-title-text">常用线路</text>
    <view class="section-line"></view>
  </view>

  <!-- 线路网格 -->
  <view class="route-grid">
    <view class="route-card" wx:for="{{ allRoutes }}" wx:key="id" bind:tap="onRouteSelect" data-route="{{ item }}">
      <view class="route-name">{{ item.name }}</view>
      <view class="route-price">¥{{ item.price }}起</view>
    </view>
  </view>
</view> 