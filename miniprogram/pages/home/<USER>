// pages/home/<USER>

import { formatDate } from '../../utils/util';

interface RouteItem {
  id: number;
  name: string;
  price: number;
}

interface CarouselItem {
  id: number;
  image: string;
  title: string;
  subtitle: string;
}

const db = wx.cloud?.database?.() || {};

Page({
  data: {
    carouselData: [
      {
        id: 1,
        image: '/images/banner1.png',
      },
      {
        id: 2,
        image: '/images/banner2.png',
      },
      {
        id: 3,
        image: '/images/banner3.png',
      }
    ] as CarouselItem[],
    allRoutes: [] as any[],
    defaultFrom: '',
    defaultTo: '',
    showCalendar: false,
    minDate: new Date().getTime(),
    maxDate: new Date(new Date().setMonth(new Date().getMonth() + 3)).getTime(),
    // 示例：无票日期，后期可从后台获取
    noTicketDates: [
      '2025-07-01', '2025-07-02', '2025-07-03', '2025-07-04',
      '2025-08-10', '2025-08-11', '2025-08-12',
      '2025-09-15', '2025-09-16', '2025-09-17'
    ],
  },

  onLoad() {
    console.log('首页加载完成');
    wx.setNavigationBarTitle({
      title: '临校速通'
    });
    // 动态设置导航栏背景色为白色
    wx.setNavigationBarColor({
      frontColor: '#000000',
      backgroundColor: '#ffffff'
    });
    this.fetchSettings();
    this.fetchRoutes();
  },

  onShow() {
    this.fetchSettings();
    this.fetchRoutes();
  },

  onSwapRoute() {
    console.log('交换路线');
    wx.showToast({
      title: '路线已交换',
      icon: 'success',
      duration: 1500
    });
  },

  onSearch() {
    console.log('开始查询');
    wx.showToast({
      title: '正在查询...',
      icon: 'loading',
      duration: 2000
    });
  },

  onRouteSelect(event: any) {
    const route = event.currentTarget.dataset.route;
    console.log('选择路线:', route);
    wx.showToast({
      title: `已选择: ${route.name}`,
      icon: 'success',
      duration: 1500
    });
  },

  // 打开日历
  onShowCalendar() {
    this.setData({ showCalendar: true });
  },

  // 关闭日历
  onCloseCalendar() {
    this.setData({ showCalendar: false });
  },

  // 自定义日期样式
  formatter(day: any) {
    // 将所有日期都设置为禁用状态
    day.type = 'disabled';
    day.className = 'disabled-day';
    day.disabled = true;
    day.topInfo = '不可选';
    day.bottomInfo = '';
    return day;
  },

  // 选择日期
  onSelectDate(event: any) {
    wx.showToast({ title: '不在预售范围内', icon: 'none' });
    this.setData({ showCalendar: true });
    return;
  },

  // 捕获所有日期点击
  onDayClick(event: any) {
    wx.showToast({ title: '不在预售范围内', icon: 'none' });
    // 阻止后续选择
    return;
  },

  fetchSettings() {
    if (!db.collection) return;
    db.collection('settings').doc('global').get().then(res => {
      const data = res.data || {};
      this.setData({
        defaultFrom: data.defaultFrom || '',
        defaultTo: data.defaultTo || ''
      });
    }).catch(() => {});
  },

  fetchRoutes() {
    if (!db.collection) return;
    db.collection('routes').get().then(res => {
      this.setData({ allRoutes: res.data || [] });
    });
  },
}); 