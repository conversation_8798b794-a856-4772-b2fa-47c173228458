<view class="admin-header">后台管理</view>

<!-- 1. 默认出发地/目的地配置 -->
<view class="admin-section">
  <view class="section-title">🚩 默认出发地/目的地配置</view>
  <van-field label="默认出发地" value="{{defaultFrom}}" placeholder="如：济南" bind:change="onDefaultFromChange" />
  <van-field label="默认目的地" value="{{defaultTo}}" placeholder="如：临沂" bind:change="onDefaultToChange" />
  <van-button type="primary" block custom-class="admin-btn" bind:click="onSaveDefault">保存配置</van-button>
</view>

<!-- 2. 常用线路配置 -->
<view class="admin-section">
  <view class="section-title">🛣️ 常用线路配置</view>
  <view class="route-form">
    <van-field label="出发地" value="{{routeFrom}}" placeholder="如：济南" bind:change="onRouteFromChange" />
    <van-field label="目的地" value="{{routeTo}}" placeholder="如：临沂" bind:change="onRouteToChange" />
    <van-field label="价格" value="{{routePrice}}" type="number" placeholder="如：45" bind:change="onRoutePriceChange" />
    <van-button type="primary" size="small" custom-class="admin-btn" bind:click="onAddRoute">添加</van-button>
  </view>
  <view class="admin-tip">已配置线路：</view>
  <view wx:for="{{routes}}" wx:key="index" class="route-row">
    <van-cell title="{{item.from}} → {{item.to}}" value="¥{{item.price}}" />
    <van-button type="danger" size="mini" custom-class="admin-btn" bind:click="onDeleteRoute" data-index="{{index}}">删除</van-button>
  </view>
</view>

<!-- 3. 查询可选日期配置 -->
<view class="admin-section">
  <view class="section-title">📅 查询可选日期配置</view>
  <van-button type="primary" block custom-class="date-config-btn" bind:click="onShowCalendar">配置售票日期</van-button>
  <van-calendar
    type="multiple"
    show="{{showCalendar}}"
    show-confirm
    color="#1989fa"
    value="{{selectedDates}}"
    bind:close="onCloseCalendar"
    bind:confirm="onCalendarConfirm"
    min-date="{{minDate}}"
    max-date="{{maxDate}}"
  />
  <view class="date-list-title">已选日期</view>
  <view wx:if="{{selectedDates.length === 0}}" class="date-empty">暂无可选日期</view>
  <view wx:for="{{selectedDates}}" wx:key="item" class="date-list-row">
    <van-cell title="{{item}}" />
    <van-button type="danger" size="mini" custom-class="date-del-btn" bind:click="onDeleteDate" data-date="{{item}}">删除</van-button>
  </view>
  <van-button type="danger" block custom-class="date-del-btn" wx:if="{{selectedDates.length > 0}}" bind:click="onBatchDelete">批量清空</van-button>
  <van-button type="primary" block custom-class="date-save-btn" bind:click="onSave">保存配置</van-button>
</view>

<!-- 4. 购票人信息查看与导出 -->
<view class="admin-section">
  <view class="section-title">👥 购票人信息</view>
  <van-button type="info" block custom-class="user-list-btn" bind:click="onGotoUserList">查看购票人信息</van-button>
</view>

<view class="admin-section admin-warning">
  ⚠️ 仅管理员/开发者可见后台管理界面
</view> 