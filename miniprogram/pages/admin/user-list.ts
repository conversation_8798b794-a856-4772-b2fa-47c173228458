Page({
  data: {
    dateList: ['2025-07-01', '2025-07-02', '2025-07-03'],
    selectedDate: '2025-07-01',
    users: [
      { name: '张三', phone: '138****8888', status: '已支付', date: '2025-07-01' },
      { name: '李四', phone: '139****6666', status: '待支付', date: '2025-07-01' },
      { name: '王五', phone: '137****1234', status: '已支付', date: '2025-07-02' },
      { name: '赵六', phone: '136****5678', status: '待支付', date: '2025-07-03' }
    ],
    filteredUsers: [] as any[]
  },
  onLoad() {
    this.filterUsersByDate(this.data.selectedDate);
  },
  onSelectDate(e: any) {
    const date = e.currentTarget.dataset.date;
    this.setData({ selectedDate: date });
    this.filterUsersByDate(date);
  },
  filterUsersByDate(date: string) {
    const filtered = this.data.users.filter((u: any) => u.date === date);
    this.setData({ filteredUsers: filtered });
  },
  onExport() {
    wx.showToast({ title: '导出成功', icon: 'success' });
  }
}); 