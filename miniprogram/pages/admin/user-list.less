@main-blue: #1989fa;

.user-list-header {
  background: @main-blue;
  color: #fff;
  padding: 24rpx 0 16rpx 0;
  text-align: center;
  font-size: 36rpx;
  font-weight: bold;
  border-radius: 0 0 24rpx 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(25,137,250,0.08);
}
.user-list-section {
  margin: 36rpx 24rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx rgba(25,137,250,0.06);
  padding: 24rpx 16rpx 10rpx 16rpx;
}
.user-export-btn {
  margin: 18rpx 0 12rpx 0;
}
.flex-row {
  display: flex;
  flex-direction: row;
}
.date-sidebar {
  width: 160rpx;
  background: #f7fafd;
  border-radius: 12rpx;
  margin-right: 18rpx;
  padding: 12rpx 0;
  box-shadow: 0 2rpx 8rpx rgba(25,137,250,0.04);
  display: flex;
  flex-direction: column;
  align-items: stretch;
}
.date-item {
  padding: 18rpx 0;
  text-align: center;
  color: #1989fa;
  font-size: 26rpx;
  cursor: pointer;
  border-left: 4rpx solid transparent;
  transition: background 0.2s, border-color 0.2s;
}
.date-item.active {
  background: #eaf6ff;
  color: #176fd6;
  border-left: 8rpx solid #1989fa;
  font-weight: bold;
}
.flex-1 {
  flex: 1;
}
.user-table {
  background: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(25,137,250,0.04);
  padding: 0 8rpx 8rpx 8rpx;
}
.user-table-header, .user-table-row {
  display: flex;
  font-size: 24rpx;
  padding: 8rpx 0;
  text-align: center;
}
.user-table-header {
  background: #eaf6ff;
  color: #1989fa;
  font-weight: bold;
  border-radius: 8rpx 8rpx 0 0;
}
.user-table-row {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  padding: 12rpx 0;
  border-bottom: 1rpx solid #e5eaf3;
}
.user-table-row:last-child { border-bottom: none; }
.cell {
  flex: 1;
  min-width: 0;
  text-align: center;
  padding: 0 8rpx;
  word-break: break-all;
}
.cell.name { flex: 1.2; }
.cell.phone { flex: 1.8; }
.cell.status { flex: 1; }
.cell.paid { color: #52c41a; font-weight: bold; }
.cell.pending { color: #faad14; font-weight: bold; }
.user-empty {
  color: #999;
  font-size: 24rpx;
  margin: 16rpx 0;
  text-align: center;
} 