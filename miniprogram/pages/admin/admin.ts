const db = wx.cloud.database();

Page({
  data: {
    defaultFrom: '',
    defaultTo: '',
    routeFrom: '',
    routeTo: '',
    routePrice: '',
    routes: [] as any[],
    selectableDates: '2025-07-01,2025-07-02,2025-07-03',
    // 新增售票日期配置相关
    showCalendar: false,
    selectedDates: ['2025-07-01', '2025-07-02', '2025-07-03'],
    minDate: new Date().getTime(),
    maxDate: new Date(new Date().setFullYear(new Date().getFullYear() + 1)).getTime(),
    users: [
      { name: '张三', phone: '138****8888', time: '2025-07-01 10:23', from: '济南', to: '临沂', date: '2025-07-01' },
      { name: '李四', phone: '139****6666', time: '2025-07-02 14:12', from: '临沂', to: '济南', date: '2025-07-02' }
    ]
  },
  onLoad() {
    this.fetchSettings();
    this.fetchRoutes();
  },
  // 拉取settings
  fetchSettings() {
    db.collection('settings').doc('global').get().then(res => {
      const data = res.data || {};
      this.setData({
        defaultFrom: data.defaultFrom || '',
        defaultTo: data.defaultTo || ''
      });
    }).catch(() => {
      // 首次无数据不报错
    });
  },
  // 拉取routes
  fetchRoutes() {
    db.collection('routes').get().then(res => {
      this.setData({ routes: res.data || [] });
    });
  },
  onDefaultFromChange(e: any) {
    this.setData({ defaultFrom: e.detail });
  },
  onDefaultToChange(e: any) {
    this.setData({ defaultTo: e.detail });
  },
  onSaveDefault() {
    const { defaultFrom, defaultTo } = this.data;
    if (!defaultFrom || !defaultTo) {
      wx.showToast({ title: '请填写完整', icon: 'none' });
      return;
    }
    wx.showLoading({ title: '保存中...' });
    db.collection('settings').doc('global').set({
      data: { defaultFrom, defaultTo }
    }).then(() => {
      wx.hideLoading();
      wx.showToast({ title: '保存成功', icon: 'success' });
    }).catch(() => {
      wx.hideLoading();
    });
  },
  onRouteFromChange(e: any) {
    this.setData({ routeFrom: e.detail });
  },
  onRouteToChange(e: any) {
    this.setData({ routeTo: e.detail });
  },
  onRoutePriceChange(e: any) {
    this.setData({ routePrice: e.detail });
  },
  onAddRoute() {
    const { routeFrom, routeTo, routePrice } = this.data;
    if (!routeFrom || !routeTo || !routePrice) {
      wx.showToast({ title: '请填写完整', icon: 'none' });
      return;
    }
    db.collection('routes').add({
      data: { from: routeFrom, to: routeTo, price: Number(routePrice) }
    }).then(() => {
      wx.showToast({ title: '添加成功', icon: 'success' });
      this.setData({ routeFrom: '', routeTo: '', routePrice: '' });
      this.fetchRoutes();
    });
  },
  onDeleteRoute(e: any) {
    const index = e.currentTarget.dataset.index;
    const routes = this.data.routes;
    const id = routes[index]._id;
    db.collection('routes').doc(id).remove().then(() => {
      wx.showToast({ title: '删除成功', icon: 'success' });
      this.fetchRoutes();
    });
  },
  onSelectableDatesChange(e: any) {
    this.setData({ selectableDates: e.detail });
  },
  onSaveDates() {
    wx.showToast({ title: '保存成功', icon: 'success' });
  },
  onExport() {
    wx.showToast({ title: '导出成功', icon: 'success' });
  },
  onShowCalendar() {
    this.setData({ showCalendar: true });
  },
  onCloseCalendar() {
    this.setData({ showCalendar: false });
  },
  onCalendarConfirm(e: any) {
    // e.detail: 选中的日期数组（时间戳）
    const dates = (e.detail || []).map((ts: number) => {
      const d = new Date(ts);
      const y = d.getFullYear();
      const m = (d.getMonth() + 1).toString().padStart(2, '0');
      const day = d.getDate().toString().padStart(2, '0');
      return `${y}-${m}-${day}`;
    });
    this.setData({ selectedDates: dates, showCalendar: false });
  },
  onDeleteDate(e: any) {
    const date = e.currentTarget.dataset.date;
    const selectedDates = this.data.selectedDates.filter((d: string) => d !== date);
    this.setData({ selectedDates });
  },
  onBatchDelete() {
    this.setData({ selectedDates: [] });
  },
  onSave() {
    wx.showToast({ title: '保存成功', icon: 'success' });
  },
  onGotoUserList() {
    wx.navigateTo({ url: '/pages/admin/user-list' });
  },
  onGotoHome() {
    wx.switchTab({
      url: '/pages/home/<USER>',
      success: () => {
        // 触发首页刷新配置（可用事件或全局变量，简单方案：触发页面onShow）
      }
    });
  }
}); 