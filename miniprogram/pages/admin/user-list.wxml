<view class="user-list-header">购票人信息</view>
<view class="user-list-section flex-row">
  <!-- 左侧日期列表 -->
  <view class="date-sidebar">
    <view wx:for="{{dateList}}" wx:key="item" class="date-item {{selectedDate === item ? 'active' : ''}}" bind:tap="onSelectDate" data-date="{{item}}">
      {{item}}
    </view>
  </view>
  <!-- 右侧购票人表格 -->
  <view class="user-table flex-1">
    <view class="user-table-header user-table-row">
      <view class="cell name">姓名</view>
      <view class="cell phone">手机号</view>
      <view class="cell status">购票状态</view>
    </view>
    <view wx:for="{{filteredUsers}}" wx:key="index" class="user-table-row">
      <view class="cell name">{{item.name}}</view>
      <view class="cell phone">{{item.phone}}</view>
      <view class="cell status {{item.status === '已支付' ? 'paid' : 'pending'}}">{{item.status}}</view>
    </view>
    <view wx:if="{{filteredUsers.length === 0}}" class="user-empty">暂无数据</view>
  </view>
</view> 