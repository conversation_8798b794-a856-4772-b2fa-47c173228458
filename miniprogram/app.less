/* 全局样式 */
page {
  background-color: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

/* 确保导航栏背景为白色 */
.navigation-bar {
  background-color: #ffffff !important;
}

/* 自定义导航栏 (kept for context, but not actively used for native nav) */
.custom-navbar {
  background: linear-gradient(135deg, #1890ff, #40a9ff);
  color: white;
  padding: 44px 16px 16px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.custom-navbar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 44px;
  background: rgba(255, 255, 255, 0.1);
}

.custom-navbar-title {
  font-size: 18px;
  font-weight: 600;
  z-index: 1;
  position: relative;
}

/* 全局 tabBar 样式调整 */
/* 注意：微信小程序的 tabBar 样式有限制，这些样式可能不会完全生效 */
.tab-bar-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
}

.tab-bar-item image {
  width: 18px !important;
  height: 18px !important;
  margin-bottom: 2px !important;
}

.tab-bar-item text {
  font-size: 10px !important;
  line-height: 1 !important;
}

/* 通用样式 */
.container {
  padding: 16px;
}

.card {
  background: white;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 16px;
}

.flex {
  display: flex;
}

.flex-center {
  align-items: center;
  justify-content: center;
}

.flex-between {
  justify-content: space-between;
}

.flex-column {
  flex-direction: column;
}

.text-center {
  text-align: center;
}

.mb-16 {
  margin-bottom: 16px;
}

.mt-16 {
  margin-top: 16px;
}

/* 自定义按钮样式 */
.custom-btn {
  width: 80vw !important;
  height: 88rpx !important;
  font-size: 36rpx !important;
  font-weight: bold !important;
  border-radius: 44rpx !important;
  background: #1989fa !important;
  color: #fff !important;
  box-shadow: 0 4rpx 16rpx rgba(25,137,250,0.08) !important;
  margin: 0 auto !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.custom-btn:active {
  transform: translateY(-1px) !important;
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4) !important;
}

/* 自定义卡片样式 */
.route-card {
  background: white;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 12px;
}

.route-card:active {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.route-name {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
}

.route-price {
  font-size: 14px;
  color: #1890ff;
  font-weight: bold;
}

/* 状态图标样式 */
.status-icon {
  width: 40px;
  height: 40px;
  background: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 18px;
  margin: 0 auto 8px;
}

.status-number {
  font-size: 18px;
  font-weight: bold;
  color: #333;
  margin-bottom: 4px;
}

.status-text {
  font-size: 12px;
  color: #666;
}

/* 功能图标样式 */
.function-icon {
  width: 48px;
  height: 48px;
  background: #f0f8ff;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1890ff;
  font-size: 24px;
  margin: 0 auto 8px;
}

.function-text {
  font-size: 12px;
  color: #333;
}

/* 推广卡片样式 */
.promotion-card {
  background: linear-gradient(135deg, #ffecd2, #fcb69f);
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 4px 16px rgba(252, 182, 159, 0.3);
  margin-bottom: 16px;
}

.promotion-content {
  color: #8b4513;
}

.promotion-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 4px;
}

.promotion-subtitle {
  font-size: 14px;
  opacity: 0.8;
}

.promotion-btn {
  background: #8b4513 !important;
  color: white !important;
  border: none !important;
  padding: 8px 16px !important;
  border-radius: 20px !important;
  font-size: 14px !important;
}

.promotion-btn:active {
  background: #a0522d !important;
} 
