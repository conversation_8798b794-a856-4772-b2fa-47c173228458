// 云开发类型定义
declare namespace WechatMiniprogram {
  interface Cloud {
    init(config?: {
      env?: string;
      traceUser?: boolean;
    }): void;
    
    callFunction(options: {
      name: string;
      data?: any;
      success?: (res: any) => void;
      fail?: (err: any) => void;
      complete?: () => void;
    }): void;
    
    database(): Database;
  }

  interface Database {
    collection(name: string): Collection;
  }

  interface Collection {
    add(options: {
      data: any;
      success?: (res: any) => void;
      fail?: (err: any) => void;
      complete?: () => void;
    }): void;
    
    where(condition: any): Collection;
    get(options?: {
      success?: (res: any) => void;
      fail?: (err: any) => void;
      complete?: () => void;
    }): void;
    
    update(options: {
      data: any;
      success?: (res: any) => void;
      fail?: (err: any) => void;
      complete?: () => void;
    }): void;
    
    remove(options?: {
      success?: (res: any) => void;
      fail?: (err: any) => void;
      complete?: () => void;
    }): void;
  }
}

// 用户信息类型
interface UserInfo {
  _id?: string;
  openid: string;
  unionid?: string;
  nickName?: string;
  avatarUrl?: string;
  gender?: number;
  country?: string;
  province?: string;
  city?: string;
  language?: string;
  phone?: string;
  createTime: Date;
  updateTime: Date;
}

// 登录响应类型
interface LoginResult {
  success: boolean;
  userInfo?: UserInfo;
  message?: string;
}

// 注册响应类型
interface RegisterResult {
  success: boolean;
  userInfo?: UserInfo;
  message?: string;
} 