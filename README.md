# 临校速通出行小程序

基于微信云开发的校园出行小程序，提供用户登录注册功能。

## 功能特性

### 🔐 用户认证
- 微信一键登录
- 用户信息管理
- 登录状态持久化

### 🏠 首页功能
- 轮播图展示
- 路线搜索
- 常用路线管理

### 👤 个人中心
- 用户信息展示
- 订单状态查看
- 功能入口管理

## 技术栈

- **前端**: 微信小程序 + TypeScript + Less
- **后端**: 微信云开发
- **UI组件**: Vant Weapp
- **数据库**: 云数据库

## 项目结构

```
lyCampucGo/
├── miniprogram/          # 小程序前端代码
│   ├── pages/           # 页面文件
│   ├── components/      # 组件文件
│   ├── utils/          # 工具类
│   ├── images/         # 图片资源
│   └── typings/        # 类型定义
├── cloudfunctions/      # 云函数
│   └── login/          # 登录云函数
└── project.config.json  # 项目配置
```

## 云开发配置

### 环境ID
- 云开发环境: `cloud1-5g83xj46e99cc3de`

### 数据库集合
- `users`: 用户信息表

### 云函数
- `login`: 用户登录注册

## 开发指南

### 1. 环境准备
1. 安装微信开发者工具
2. 导入项目
3. 配置云开发环境

### 2. 本地开发
```bash
# 安装依赖
npm install

# 构建npm
# 在微信开发者工具中点击"工具" -> "构建npm"
```

### 3. 云函数部署
```bash
# 在微信开发者工具中右键点击 cloudfunctions/login
# 选择"上传并部署：云端安装依赖"
```

### 4. 数据库配置
1. 在云开发控制台创建 `users` 集合
2. 设置集合权限为"仅创建者可读写"

## 用户登录流程

1. **用户点击登录按钮**
2. **调用微信登录API获取code**
3. **调用云函数login处理登录逻辑**
4. **云函数获取用户openid**
5. **检查用户是否已存在**
   - 存在：更新登录时间
   - 不存在：创建新用户记录
6. **返回用户信息到前端**
7. **保存用户信息到本地存储**

## 数据库设计

### users 集合
```javascript
{
  _id: "用户ID",
  openid: "微信openid",
  unionid: "微信unionid",
  nickName: "用户昵称",
  avatarUrl: "头像URL",
  phone: "手机号",
  createTime: "创建时间",
  updateTime: "更新时间"
}
```

## 部署说明

1. **上传云函数**
   - 在微信开发者工具中右键点击 `cloudfunctions/login`
   - 选择"上传并部署：云端安装依赖"

2. **配置数据库**
   - 在云开发控制台创建 `users` 集合
   - 设置集合权限

3. **发布小程序**
   - 在微信开发者工具中点击"上传"
   - 在微信公众平台提交审核

## 注意事项

1. **云开发环境ID**: 确保 `app.json` 和云函数中的环境ID一致
2. **数据库权限**: 设置合适的数据库读写权限
3. **用户隐私**: 遵守微信小程序用户隐私保护规范
4. **包大小**: 注意小程序包大小限制（2MB）

## 版本信息

- 当前版本: v2.14.88
- 最后更新: 2025-07-27 